<?php
include_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

$patient_id = isset($_GET['id']) ? $_GET['id'] : die('ID du patient requis');
$page_title = "Détails du Patient";
include 'includes/header.php';
?>

<!-- Ajout du CSS pour jQuery UI Autocomplete -->
<link href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    /* Variables CSS pour la cohérence des couleurs */
    :root {
        --primary-color: #2563eb;
        --primary-light: #3b82f6;
        --primary-dark: #1d4ed8;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #06b6d4;
        --light-bg: #f8fafc;
        --border-color: #e2e8f0;
        --text-muted: #64748b;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    }

    /* Style général de la page */
    body {
        background-color: var(--light-bg);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* Header amélioré */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
        box-shadow: var(--shadow-lg);
    }

    .page-header h1 {
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
    }

    .page-header .breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        margin-top: 1rem;
    }

    .page-header .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
    }

    .page-header .breadcrumb-item.active {
        color: white;
    }

    /* Cards améliorées */
    .card {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        background: white;
    }

    .card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }

    /* Onglets améliorés */
    .nav-tabs {
        border: none;
        gap: 0.5rem;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        color: var(--text-muted);
        background: rgba(255, 255, 255, 0.7);
        transition: all 0.3s ease;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .nav-tabs .nav-link:hover {
        background: rgba(var(--primary-color), 0.1);
        color: var(--primary-color);
        transform: translateY(-2px);
    }

    .nav-tabs .nav-link.active {
        background: var(--primary-color);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .nav-tabs .nav-link i {
        font-size: 1.1rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.5rem;
    }

    /* Style pour s'assurer que l'autocomplétion apparaît au-dessus du modal */
    .ui-autocomplete {
        z-index: 2000 !important;
        max-height: 200px;
        overflow-y: auto;
        overflow-x: hidden;
        background-color: #fff;
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        box-shadow: var(--shadow-lg);
    }

    .ui-menu-item {
        padding: 12px 16px;
        cursor: pointer;
        font-size: 14px;
        border-bottom: 1px solid var(--border-color);
    }

    .ui-menu-item:hover {
        background-color: var(--light-bg);
    }

    .ui-state-active {
        background-color: var(--primary-color) !important;
        border: none !important;
        color: white !important;
        margin: 0 !important;
    }

    /* Styles pour le modal plein écran */
    .modal-fullscreen {
        padding: 0 !important;
    }

    .modal-fullscreen .modal-dialog {
        width: 100% !important;
        max-width: none;
        height: 100%;
        margin: 0;
    }

    .modal-fullscreen .modal-content {
        height: 100%;
        border: 0;
        border-radius: 0;
    }

    .modal-fullscreen .modal-body {
        overflow-y: auto;
        padding: 1rem;
        height: calc(100vh - 120px);
    }

    .modal-fullscreen .container-fluid {
        height: 100%;
        padding: 0;
    }

    .modal-fullscreen .row {
        height: 100%;
        margin: 0;
    }

    .modal-fullscreen .col-md-3 {
        height: 100%;
        padding: 0.5rem;
    }

    .modal-fullscreen .card {
        height: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
    }

    .modal-fullscreen .card-body {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
    }

    .modal-fullscreen textarea.form-control {
        height: calc(100% - 30px);
        min-height: 100px;
        resize: none;
    }

    /* Style pour les sections de contenu */
    .modal-fullscreen .content-section {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    /* Ajustement du footer du modal */
    .modal-fullscreen .modal-footer {
        padding: 1rem;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
    }

    /* Style pour le texte préformaté dans l'historique */
    .text-pre-wrap {
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 0.9em;
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 4px;
    }

    .history-btn.active {
        background-color: #0d6efd;
        color: white;
    }

    /* Informations patient améliorées */
    .patient-info-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-left: 4px solid var(--primary-color);
    }

    .info-item {
        padding: 1rem;
        border-radius: 0.75rem;
        background: rgba(255, 255, 255, 0.8);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .info-item:hover {
        background: rgba(var(--primary-color), 0.05);
        transform: translateX(4px);
    }

    .info-label {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-size: 1.1rem;
        color: #1f2937;
        font-weight: 500;
    }

    /* Tableaux améliorés */
    .table {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table thead th {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.875rem;
        letter-spacing: 0.05em;
        border: none;
        padding: 1rem;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: var(--light-bg);
        transform: scale(1.01);
    }

    .table tbody td {
        padding: 1rem;
        border-color: var(--border-color);
        vertical-align: middle;
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 0.75rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    }

    .btn-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    }

    .btn-info {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%);
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Badges de statut améliorés */
    .badge {
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
    }

    .bg-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%) !important;
    }

    .bg-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%) !important;
    }

    .bg-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }

        .nav-tabs .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
        }

        .modal-fullscreen .col-md-3 {
            height: auto;
            margin-bottom: 1rem;
        }

        .modal-fullscreen .card {
            height: auto;
            margin-bottom: 1rem;
        }

        .modal-fullscreen textarea.form-control {
            height: 150px;
        }

        .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    /* Améliorations des modals */
    .modal-content {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-lg);
    }

    .modal-header {
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0;
        padding: 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        border-top: 2px solid var(--border-color);
        border-radius: 0 0 1rem 1rem;
        padding: 1.5rem;
    }

    /* Améliorations des formulaires */
    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: var(--text-muted);
        margin-bottom: 0.5rem;
    }

    /* Animation d'entrée pour les cartes */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .card {
        animation: slideInUp 0.6s ease-out;
    }

    /* Amélioration des toasts */
    .toast {
        border-radius: 0.75rem;
        box-shadow: var(--shadow-lg);
    }
</style>

<!-- Ajout du conteneur de toasts -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <!-- Le message sera inséré ici -->
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<!-- Header de la page amélioré -->
<div class="page-header">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1>
                    <i class="fas fa-user-md me-3"></i>
                    <span id="patient-header-name">Profil Patient</span>
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="dashboard.php">
                                <i class="fas fa-home"></i> Accueil
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="patients.php">
                                <i class="fas fa-users"></i> Patients
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-user"></i> Détails
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <a href="patients.php" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
                <button class="btn btn-light edit-patient-info">
                    <i class="fas fa-edit"></i> Modifier
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid py-4">

    <!-- Card avec les onglets -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#infos">
                        <i class="fas fa-user"></i> Informations personnelles
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#antecedents">
                        <i class="fas fa-history"></i> Antécédents
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#traitements">
                        <i class="fas fa-pills"></i> Traitements en cours
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#visites">
                        <i class="fas fa-calendar-check"></i> Visites <span class="badge bg-primary" id="visites-count">0</span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <!-- Onglet Informations personnelles -->
                <div class="tab-pane fade" id="infos">
                    <div class="row">
                        <div class="col-12">
                            <div class="patient-info-card card border-0 mb-4">
                                <div class="card-body p-4">
                                    <div class="row" id="patient-info">
                                        <!-- Les informations seront chargées ici via JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet Antécédents -->
                <div class="tab-pane fade" id="antecedents">
                    <div class="card border-0">
                        <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-history text-primary me-2"></i>
                                Historique médical
                            </h5>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAntecedentModal">
                                <i class="fas fa-plus"></i> Ajouter un antécédent
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Description</th>
                                            <th>Date d'ajout</th>
                                            <th width="100">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="antecedents-list">
                                        <!-- Les antécédents seront chargés ici -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet Traitements -->
                <div class="tab-pane fade" id="traitements">
                    <div class="card border-0">
                        <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-pills text-primary me-2"></i>
                                Traitements médicamenteux
                            </h5>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTraitementModal">
                                <i class="fas fa-plus"></i> Ajouter un traitement
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Médicament</th>
                                            <th>Posologie</th>
                                            <th>Date début</th>
                                            <th>Date fin</th>
                                            <th>Statut</th>
                                            <th width="120">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="traitements-list">
                                        <!-- Les traitements seront chargés ici -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet Visites -->
                <div class="tab-pane fade show active" id="visites">
                    <div class="card border-0">
                        <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-check text-primary me-2"></i>
                                Consultations et visites
                            </h5>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVisiteModal">
                                <i class="fas fa-plus"></i> Nouvelle Visite
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Heure</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Motif</th>
                                            <th width="150">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="visites-list">
                                        <!-- Les visites seront chargées ici -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Modification Patient -->
<div class="modal fade" id="editPatientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>
                    Modifier les informations du patient
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPatientForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="edit_nom" name="nom" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="edit_prenom" name="prenom" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_date_naissance" class="form-label">Date de naissance</label>
                            <input type="date" class="form-control" id="edit_date_naissance" name="date_naissance">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_telephone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="edit_telephone" name="telephone" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="edit_adresse" name="adresse" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit_numero_assurance_sociale" class="form-label">N° Assurance Sociale</label>
                        <input type="text" class="form-control" id="edit_numero_assurance_sociale" name="numero_assurance_sociale">
                    </div>
                    <input type="hidden" id="edit_id" name="id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Ajout Antécédent -->
<div class="modal fade" id="addAntecedentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un antécédent</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addAntecedentForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Ajout Traitement -->
<div class="modal fade" id="addTraitementModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un traitement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTraitementForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nom_medicament" class="form-label">Médicament</label>
                        <input type="text" class="form-control" id="nom_medicament" name="nom_medicament" required>
                    </div>
                    <div class="mb-3">
                        <label for="posologie" class="form-label">Posologie</label>
                        <textarea class="form-control" id="posologie" name="posologie" rows="2" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_debut" class="form-label">Date de début</label>
                            <input type="date" class="form-control" id="date_debut" name="date_debut" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="date_fin" class="form-label">Date de fin (optionnelle)</label>
                            <input type="date" class="form-control" id="date_fin" name="date_fin">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Ajout Visite -->
<div class="modal fade" id="addVisiteModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Nouvelle visite de : <span id="patient-name-title"></span></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addVisiteForm">
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <!-- Colonne 1: Informations de base -->
                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Informations de base</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="date_visite" class="form-label">Date</label>
                                            <input type="date" class="form-control" id="date_visite" name="date_visite" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="heure_visite" class="form-label">Heure</label>
                                            <input type="time" class="form-control" id="heure_visite" name="heure_visite" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="type_visite" class="form-label">Type de visite</label>
                                            <select class="form-select" id="type_visite" name="type_visite" required>
                                                <option value="cabinet">Cabinet</option>
                                                <option value="domicile">À domicile</option>
                                                <option value="controle">Contrôle</option>
                                            </select>
                                        </div>
                                        <input type="hidden" id="status" name="status" value="fait">
                                        <div class="mb-3">
                                            <label for="honoraire" class="form-label">Honoraire</label>
                                            <div class="input-group">
                                                <input type="number" step="0.01" class="form-control" id="honoraire" name="honoraire">
                                                <span class="input-group-text">TND</span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">Notes</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Notes additionnelles..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Colonne 2: Motif et Histoire -->
                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Motif et Histoire</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="motif" class="form-label">Motif de consultation</label>
                                                <button type="button" class="btn btn-sm btn-outline-secondary history-btn" data-field="motif">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                            <input type="text" class="form-control" id="motif" name="motif" rows="4"/>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="histoire_maladie" class="form-label">Histoire de la maladie</label>
                                                <button type="button" class="btn btn-sm btn-outline-secondary history-btn" data-field="histoire_maladie">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                            <textarea class="form-control" id="histoire_maladie" name="histoire_maladie" rows="8"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Colonne 3: Examens -->
                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Examens</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Constantes vitales -->
                                        <div class="vitals-container mb-3">
                                            <h6 class="text-muted mb-2">Constantes vitales</h6>
                                            <div class="vitals-grid">
                                                <div class="vital-item">
                                                    <span class="vital-label">T°</span>
                                                    <input type="number" step="0.1" class="form-control vital-input" id="temperature" name="temperature" placeholder="36.5">
                                                    <span class="vital-unit">°C</span>
                                                </div>
                                                <div class="vital-item">
                                                    <span class="vital-label">Poids</span>
                                                    <input type="number" step="0.1" class="form-control vital-input" id="poids" name="poids" placeholder="65.0">
                                                    <span class="vital-unit">kg</span>
                                                </div>
                                                <div class="vital-item">
                                                    <span class="vital-label">TA</span>
                                                    <input type="text" class="form-control vital-input" id="ta" name="ta" placeholder="12/8">
                                                    <span class="vital-unit">mmHg</span>
                                                </div>
                                                <div class="vital-item">
                                                    <span class="vital-label">Fc</span>
                                                    <input type="number" class="form-control vital-input" id="fc" name="fc" placeholder="72">
                                                    <span class="vital-unit">bpm</span>
                                                </div>
                                                <div class="vital-item">
                                                    <span class="vital-label">SaO2</span>
                                                    <input type="number" class="form-control vital-input" id="sao2" name="sao2" placeholder="99">
                                                    <span class="vital-unit">%</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="examens_cliniques" class="form-label">Examens cliniques</label>
                                                <button type="button" class="btn btn-sm btn-outline-secondary history-btn" data-field="examens_cliniques">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                            <textarea class="form-control" id="examens_cliniques" name="examens_cliniques" rows="6"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="examens_complementaires" class="form-label">Examens complémentaires</label>
                                                <button type="button" class="btn btn-sm btn-outline-secondary history-btn" data-field="examens_complementaires">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                            <textarea class="form-control" id="examens_complementaires" name="examens_complementaires" rows="6"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Colonne 4: Diagnostic et CAT -->
                            <div class="col-md-3">
                                <div class="card h-100">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Diagnostic et Conduite</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="diagnostic" class="form-label">Diagnostic</label>
                                                <button type="button" class="btn btn-sm btn-outline-secondary history-btn" data-field="diagnostic">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                            <textarea class="form-control" id="diagnostic" name="diagnostic" rows="4"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label for="cat" class="form-label">Conduite à tenir (CAT)</label>
                                                <button type="button" class="btn btn-sm btn-outline-secondary history-btn" data-field="cat">
                                                    <i class="fas fa-history"></i>
                                                </button>
                                            </div>
                                            <textarea class="form-control" id="cat" name="cat" rows="6"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Détails Visite -->
<div class="modal fade" id="viewVisiteModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">Détails de la Visite</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="container-fluid" id="visite-details">
                    <!-- Les détails seront chargés ici -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary edit-visite-btn">
                    <i class="fas fa-edit"></i> Modifier
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour l'historique -->
<div class="modal fade" id="historiqueModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">Historique des valeurs</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group" id="historique-list">
                    <!-- Les valeurs seront chargées ici -->
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<!-- Ajout de jQuery UI pour l'autocomplétion -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    const patientId = <?php echo $patient_id; ?>;
    let patientName = '';
    
    // Fonction pour formater la date au format YYYY-MM-DD
    function formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    // Fonction pour formater l'heure au format HH:mm
    function formatTime(date) {
        return date.toTimeString().slice(0, 5);
    }

    // Fonction pour définir les valeurs par défaut de date et heure
    function setDefaultDateTime() {
        const now = new Date();
        $('#date_visite').val(formatDate(now));
        $('#heure_visite').val(formatTime(now));
    }

    // Initialiser les valeurs par défaut lors de l'ouverture du modal
    $('#addVisiteModal').on('show.bs.modal', function() {
        setDefaultDateTime();
    });

    // Charger les données du patient
    function loadPatientData() {
        $.get('api/patient_details.php?id=' + patientId, function(data) {
            // Afficher les informations du patient
            const patient = data.patient;
            patientName = patient.nom + ' ' + patient.prenom;
            $('#patient-name-title').text(patientName);

            // Mettre à jour le nom dans le header
            $('#patient-header-name').text(patientName);
            const patientInfo = `
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5 class="text-primary mb-4 d-flex align-items-center">
                            <i class="fas fa-user me-2"></i>
                            Informations personnelles
                        </h5>
                        <div class="info-item">
                            <div class="info-label">Nom complet</div>
                            <div class="info-value">${patient.nom} ${patient.prenom}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Date de naissance</div>
                            <div class="info-value">${patient.date_naissance ? new Date(patient.date_naissance).toLocaleDateString('fr-FR') : 'Non renseignée'}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-4">
                        <h5 class="text-primary mb-4 d-flex align-items-center">
                            <i class="fas fa-address-book me-2"></i>
                            Coordonnées
                        </h5>
                        <div class="info-item">
                            <div class="info-label">Téléphone</div>
                            <div class="info-value">
                                ${patient.telephone ? `<a href="tel:${patient.telephone}" class="text-decoration-none">${patient.telephone}</a>` : 'Non renseigné'}
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Adresse</div>
                            <div class="info-value">${patient.adresse || 'Non renseignée'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">N° Assurance Sociale</div>
                            <div class="info-value">${patient.numero_assurance_sociale || 'Non renseigné'}</div>
                        </div>
                    </div>
                </div>
            `;
            $('#patient-info').html(patientInfo);

            // Afficher les antécédents
            const antecedentsList = data.antecedents.map(ant => `
                <tr>
                    <td>${ant.description}</td>
                    <td>${new Date(ant.date_ajout).toLocaleDateString('fr-FR')}</td>
                    <td>
                        <button class="btn btn-sm btn-danger delete-antecedent" data-id="${ant.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
            $('#antecedents-list').html(antecedentsList);

            // Afficher les traitements
            const traitementsList = data.traitements.map(trait => `
                <tr>
                    <td>${trait.nom_medicament}</td>
                    <td>${trait.posologie}</td>
                    <td>${new Date(trait.date_debut).toLocaleDateString('fr-FR')}</td>
                    <td>${trait.date_fin ? new Date(trait.date_fin).toLocaleDateString('fr-FR') : '-'}</td>
                    <td>
                        <span class="badge bg-${trait.statut === 'en_cours' ? 'success' : 'secondary'}">
                            ${trait.statut}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-warning change-status" data-id="${trait.id}">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-traitement" data-id="${trait.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
            $('#traitements-list').html(traitementsList);
        });
    }

    // Charger les données initiales
    loadPatientData();
    loadVisites();

    // Initialisation du toast
    var successToast = new bootstrap.Toast(document.getElementById('successToast'));
    
    // Fonction pour afficher le toast avec un message
    function showToast(message) {
        $('.toast-body').text(message);
        successToast.show();
    }

    // Autocomplétion pour le champ médicament
    $('#nom_medicament').autocomplete({
        source: 'api/medicaments.php',
        minLength: 2,
        select: function(event, ui) {
            $(this).val(ui.item.value);
            return false;
        }
    });

    // Modification des callbacks pour utiliser les toasts
    $('#addAntecedentForm').on('submit', function(e) {
        e.preventDefault();
        const formData = {
            patient_id: patientId,
            description: $('#description').val()
        };
        
        $.ajax({
            url: 'api/antecedents.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#addAntecedentModal').modal('hide');
                loadPatientData();
                showToast('Antécédent ajouté avec succès');
            },
            error: function() {
                showToast('Erreur lors de l\'ajout de l\'antécédent');
            }
        });
    });

    $('#addTraitementForm').on('submit', function(e) {
        e.preventDefault();
        const formData = {
            patient_id: patientId,
            nom_medicament: $('#nom_medicament').val(),
            posologie: $('#posologie').val(),
            date_debut: $('#date_debut').val(),
            date_fin: $('#date_fin').val() || null
        };
        
        $.ajax({
            url: 'api/traitements.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#addTraitementModal').modal('hide');
                loadPatientData();
                showToast('Traitement ajouté avec succès');
            },
            error: function() {
                showToast('Erreur lors de l\'ajout du traitement');
            }
        });
    });

    $(document).on('click', '.delete-antecedent', function() {
        if(confirm('Êtes-vous sûr de vouloir supprimer cet antécédent ?')) {
            const id = $(this).data('id');
            $.ajax({
                url: 'api/antecedents.php?id=' + id,
                type: 'DELETE',
                success: function() {
                    loadPatientData();
                    showToast('Antécédent supprimé avec succès');
                },
                error: function() {
                    showToast('Erreur lors de la suppression de l\'antécédent');
                }
            });
        }
    });

    $(document).on('click', '.delete-traitement', function() {
        if(confirm('Êtes-vous sûr de vouloir supprimer ce traitement ?')) {
            const id = $(this).data('id');
            $.ajax({
                url: 'api/traitements.php?id=' + id,
                type: 'DELETE',
                success: function() {
                    loadPatientData();
                    showToast('Traitement supprimé avec succès');
                },
                error: function() {
                    showToast('Erreur lors de la suppression du traitement');
                }
            });
        }
    });

    $(document).on('click', '.change-status', function() {
        const id = $(this).data('id');
        $.ajax({
            url: 'api/traitements.php?id=' + id + '&action=toggle_status',
            type: 'PUT',
            success: function() {
                loadPatientData();
                showToast('Statut du traitement mis à jour');
            },
            error: function() {
                showToast('Erreur lors du changement de statut');
            }
        });
    });

    // Réinitialisation des formulaires lors de la fermeture des modals
    $('.modal').on('hidden.bs.modal', function() {
        const form = $(this).find('form');
        if (form.length > 0) {
            form[0].reset();
            // Réinitialiser les données personnalisées
            form.removeData('edit-id');
        }
        
        // Si c'est le modal d'ajout de visite, réinitialiser complètement
        if ($(this).attr('id') === 'addVisiteModal') {
            resetVisiteForm();
        }
    });

    // Gérer le clic sur le bouton de modification des informations
    $(document).on('click', '.edit-patient-info', function() {
        $.get('api/patients.php?id=' + patientId, function(data) {
            $('#edit_id').val(data.id);
            $('#edit_nom').val(data.nom);
            $('#edit_prenom').val(data.prenom);
            $('#edit_date_naissance').val(data.date_naissance);
            $('#edit_telephone').val(data.telephone);
            $('#edit_adresse').val(data.adresse);
            $('#edit_numero_assurance_sociale').val(data.numero_assurance_sociale);
            $('#editPatientModal').modal('show');
        });
    });

    // Gérer la soumission du formulaire de modification du patient
    $('#editPatientForm').on('submit', function(e) {
        e.preventDefault();
        const formData = {
            id: $('#edit_id').val(),
            nom: $('#edit_nom').val(),
            prenom: $('#edit_prenom').val(),
            date_naissance: $('#edit_date_naissance').val(),
            telephone: $('#edit_telephone').val(),
            adresse: $('#edit_adresse').val(),
            numero_assurance_sociale: $('#edit_numero_assurance_sociale').val()
        };

        $.ajax({
            url: 'api/patients.php?id=' + formData.id,
            type: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#editPatientModal').modal('hide');
                loadPatientData(); // Recharger les données du patient

                // Mettre à jour le nom dans le header immédiatement
                const newPatientName = $('#edit_nom').val() + ' ' + $('#edit_prenom').val();
                $('#patient-header-name').text(newPatientName);
                patientName = newPatientName;

                showToast('Informations du patient mises à jour avec succès');
            },
            error: function() {
                showToast('Erreur lors de la mise à jour des informations');
            }
        });
    });

    // Charger les visites du patient
    function loadVisites() {
        $.get('api/visites.php?patient_id=' + patientId, function(data) {
            const visitesList = data.map(visite => `
                <tr>
                    <td>${new Date(visite.date_visite).toLocaleDateString('fr-FR')}</td>
                    <td>${visite.heure_visite}</td>
                    <td>${visite.type_visite}</td>
                    <td>
                        <span class="badge bg-${getBadgeColor(visite.status)}">
                            ${visite.status}
                        </span>
                    </td>
                    <td>${visite.motif || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-info view-visite" data-id="${visite.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary edit-visite" data-id="${visite.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-visite" data-id="${visite.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
            $('#visites-list').html(visitesList);
        });
    }

    // Fonction pour obtenir la couleur du badge selon le status
    function getBadgeColor(status) {
        switch(status) {
            case 'rendez-vous': return 'warning';
            case 'present': return 'info';
            case 'fait': return 'success';
            default: return 'secondary';
        }
    }

    // Variable pour stocker la visite en cours
    let currentVisite = null;

    // Voir les détails d'une visite
    $(document).on('click', '.view-visite', function() {
        const id = $(this).data('id');
        $.get('api/visites.php?id=' + id, function(visite) {
            currentVisite = visite; // Stocker la visite courante
            const details = `
                <div class="row">
                    <!-- Colonne 1: Informations de base -->
                    <div class="col-md-3">
                        <div class="card h-100">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Informations de base</h6>
                                <button type="button" class="btn btn-sm btn-primary edit-visite-btn" data-id="${visite.id}">
                                    <i class="fas fa-edit"></i> Modifier
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Date</label>
                                    <p class="mb-2">${new Date(visite.date_visite).toLocaleDateString('fr-FR')}</p>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Heure</label>
                                    <p class="mb-2">${visite.heure_visite}</p>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Type</label>
                                    <p class="mb-2">${visite.type_visite}</p>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Status</label>
                                    <p class="mb-2">
                                        <span class="badge bg-${getBadgeColor(visite.status)}">${visite.status}</span>
                                    </p>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Honoraire</label>
                                    <p class="mb-2">${visite.honoraire ? visite.honoraire + ' TND' : '-'}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Colonne 2: Motif et Histoire -->
                    <div class="col-md-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Motif et Histoire</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Motif de consultation</label>
                                    <div class="border rounded p-2 bg-light">
                                        ${visite.motif || '-'}
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Histoire de la maladie</label>
                                    <div class="border rounded p-2 bg-light">
                                        ${visite.histoire_maladie || '-'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Colonne 3: Examens -->
                    <div class="col-md-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Examens</h6>
                            </div>
                            <div class="card-body">
                                <!-- Constantes vitales -->
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <label class="fw-bold text-primary">Température</label>
                                        <p class="mb-1">${visite.temperature ? visite.temperature + ' °C' : '-'}</p>
                                    </div>
                                    <div class="col-6">
                                        <label class="fw-bold text-primary">Poids</label>
                                        <p class="mb-1">${visite.poids ? visite.poids + ' kg' : '-'}</p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <label class="fw-bold text-primary">TA</label>
                                        <p class="mb-1">${visite.ta ? visite.ta + ' mmHg' : '-'}</p>
                                    </div>
                                    <div class="col-6">
                                        <label class="fw-bold text-primary">Fc</label>
                                        <p class="mb-1">${visite.fc ? visite.fc + ' bpm' : '-'}</p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <label class="fw-bold text-primary">SaO2</label>
                                        <p class="mb-1">${visite.sao2 ? visite.sao2 + ' %' : '-'}</p>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Examens cliniques</label>
                                    <div class="border rounded p-2 bg-light">
                                        ${visite.examens_cliniques || '-'}
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Examens complémentaires</label>
                                    <div class="border rounded p-2 bg-light">
                                        ${visite.examens_complementaires || '-'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Colonne 4: Diagnostic et CAT -->
                    <div class="col-md-3">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Diagnostic et Conduite</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Diagnostic</label>
                                    <div class="border rounded p-2 bg-light">
                                        ${visite.diagnostic || '-'}
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Conduite à tenir</label>
                                    <div class="border rounded p-2 bg-light">
                                        ${visite.cat || '-'}
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="fw-bold text-primary">Notes</label>
                                    <div class="border rounded p-2 bg-light">
                                        ${visite.notes || '-'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#visite-details').html(details);
            $('#viewVisiteModal').modal('show');
        });
    });

    // Gérer le clic sur le bouton Modifier dans le modal de détails
    $(document).on('click', '.edit-visite-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        if (!currentVisite) return;
        
        // Fermer le modal de visualisation
        $('#viewVisiteModal').modal('hide');
        
        // Remplir le formulaire avec les données
        $('#date_visite').val(currentVisite.date_visite);
        $('#heure_visite').val(currentVisite.heure_visite);
        $('#type_visite').val(currentVisite.type_visite);
        $('#status').val('fait');
        $('#honoraire').val(currentVisite.honoraire);
        $('#motif').val(currentVisite.motif);
        $('#histoire_maladie').val(currentVisite.histoire_maladie);
        $('#examens_cliniques').val(currentVisite.examens_cliniques);
        $('#examens_complementaires').val(currentVisite.examens_complementaires);
        $('#diagnostic').val(currentVisite.diagnostic);
        $('#cat').val(currentVisite.cat);
        $('#notes').val(currentVisite.notes);
        $('#temperature').val(currentVisite.temperature);
        $('#poids').val(currentVisite.poids);
        $('#ta').val(currentVisite.ta);
        $('#fc').val(currentVisite.fc);
        $('#sao2').val(currentVisite.sao2);
        
        // Modifier le titre et le bouton du modal
        $('#addVisiteModal .modal-title').html(`Modifier la visite du ${new Date(currentVisite.date_visite).toLocaleDateString('fr-FR')}`);
        $('#addVisiteModal button[type="submit"]').html('<i class="fas fa-save"></i> Enregistrer les modifications');
        
        // Ajouter l'ID de la visite au formulaire
        $('#addVisiteForm').data('edit-id', currentVisite.id);
        
        // Attendre que le modal de visualisation soit complètement fermé
        setTimeout(function() {
            // Afficher le modal d'édition
            $('#addVisiteModal').modal('show');
        }, 500);
    });

    // Modifier la soumission du formulaire pour gérer l'édition
    $('#addVisiteForm').on('submit', function(e) {
        e.preventDefault();
        const editId = $(this).data('edit-id');
        const isEdit = !!editId;
        
        const formData = {
            patient_id: patientId,
            date_visite: $('#date_visite').val(),
            heure_visite: $('#heure_visite').val(),
            type_visite: $('#type_visite').val(),
            status: $('#status').val(),
            honoraire: $('#honoraire').val(),
            motif: $('#motif').val(),
            histoire_maladie: $('#histoire_maladie').val(),
            examens_cliniques: $('#examens_cliniques').val(),
            examens_complementaires: $('#examens_complementaires').val(),
            diagnostic: $('#diagnostic').val(),
            cat: $('#cat').val(),
            notes: $('#notes').val(),
            temperature: $('#temperature').val(),
            poids: $('#poids').val(),
            ta: $('#ta').val(),
            fc: $('#fc').val(),
            sao2: $('#sao2').val()
        };

        const url = isEdit ? `api/visites.php?id=${editId}` : 'api/visites.php';
        const method = isEdit ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            type: method,
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                $('#addVisiteModal').modal('hide');
                loadVisites();
                showToast(isEdit ? 'Visite modifiée avec succès' : 'Visite ajoutée avec succès');
                resetVisiteForm();
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Une erreur est survenue';
                showToast(message, 'error');
            }
        });
    });

    // Fonction pour réinitialiser le formulaire
    function resetVisiteForm() {
        const form = $('#addVisiteForm');
        if (form.length > 0) {
            form[0].reset();
            form.removeData('edit-id');
        }
        
        // Réinitialiser le titre et le bouton
        $('#addVisiteModal .modal-title').text('Nouvelle visite de : ' + patientName);
        $('#addVisiteModal button[type="submit"]').html('<i class="fas fa-save"></i> Enregistrer');
        
        // Réinitialiser les valeurs par défaut
        const now = new Date();
        $('#date_visite').val(formatDate(now));
        $('#heure_visite').val(formatTime(now));
        $('#type_visite').val('cabinet');
        $('#status').val('fait');
        $('#honoraire').val('80');
        
        // Vider les champs de texte
        $('#motif, #histoire_maladie, #examens_cliniques, #examens_complementaires, #diagnostic, #cat, #notes, #temperature, #poids, #ta, #fc, #sao2').val('');
        
        // Réinitialiser la variable globale
        currentVisite = null;
    }

    // Charger les visites au chargement de la page
    loadVisites();

    // Ajouter après loadVisites()
    function updateVisitesCount() {
        $.get('api/visites.php?patient_id=' + patientId, function(data) {
            $('#visites-count').text(data.length);
        });
    }

    // Historique des valeurs
    let historiqueData = {};

    function loadHistoriqueData() {
        $.get('api/visites.php?patient_id=' + patientId, function(data) {
            historiqueData = data.reduce((acc, visite) => {
                const fields = ['motif', 'histoire_maladie', 'examens_cliniques', 
                              'examens_complementaires', 'diagnostic', 'cat'];
                
                fields.forEach(field => {
                    if (!acc[field]) acc[field] = [];
                    if (visite[field]) {
                        acc[field].push({
                            value: visite[field],
                            date: new Date(visite.date_visite).toLocaleDateString('fr-FR')
                        });
                    }
                });
                return acc;
            }, {});
        });
    }

    // Gestionnaire de clic pour les boutons d'historique
    $(document).on('click', '.history-btn', function(e) {
        e.preventDefault();
        const field = $(this).data('field');
        const fieldData = historiqueData[field] || [];
        
        let historyHtml = '';
        if (fieldData.length > 0) {
            historyHtml = fieldData.map((item, index) => `
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">Visite du ${item.date}</small>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-primary copy-to-field" data-value="${encodeURIComponent(item.value)}" data-field="${field}">
                                <i class="fas fa-paste"></i> Copier dans le champ
                            </button>
                            <button class="btn btn-sm btn-outline-secondary copy-to-clipboard" data-value="${encodeURIComponent(item.value)}">
                                <i class="fas fa-copy"></i> Copier
                            </button>
                        </div>
                    </div>
                    <div class="text-pre-wrap">${item.value}</div>
                </div>
            `).join('');
        } else {
            historyHtml = '<div class="list-group-item">Aucun historique disponible</div>';
        }
        
        $('#historique-list').html(historyHtml);
        $('#historiqueModal').modal('show');
        
        // Stocker le champ actif pour une utilisation ultérieure
        $('#historiqueModal').data('activeField', field);
    });

    // Copier dans le champ du formulaire
    $(document).on('click', '.copy-to-field', function() {
        const value = decodeURIComponent($(this).data('value'));
        const field = $(this).data('field');
        
        // Fermer le modal d'historique
        $('#historiqueModal').modal('hide');
        
        // Attendre la fermeture du modal d'historique
        $('#historiqueModal').on('hidden.bs.modal', function() {
            // Mettre à jour le champ correspondant dans le formulaire
            $(`#${field}`).val(value);
            
            // Réafficher le modal de visite
            $('#addVisiteModal').modal('show');
            
            // Afficher un toast de confirmation
            showToast('Texte copié dans le champ');
        });
    });

    // Copier dans le presse-papier
    $(document).on('click', '.copy-to-clipboard', async function() {
        const value = decodeURIComponent($(this).data('value'));
        const button = $(this);
        
        try {
            await navigator.clipboard.writeText(value);
            
            // Feedback visuel temporaire
            const originalText = button.html();
            button.html('<i class="fas fa-check"></i> Copié!');
            button.removeClass('btn-outline-secondary').addClass('btn-success');
            
            setTimeout(() => {
                button.html(originalText);
                button.removeClass('btn-success').addClass('btn-outline-secondary');
            }, 2000);
            
            // Afficher un toast de confirmation
            showToast('Texte copié dans le presse-papier');
        } catch (err) {
            // Fallback pour les navigateurs qui ne supportent pas l'API Clipboard
            const textarea = document.createElement('textarea');
            textarea.value = value;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                showToast('Texte copié dans le presse-papier');
            } catch (err) {
                showToast('Erreur lors de la copie', 'error');
            }
            document.body.removeChild(textarea);
        }
    });

    // Fonction pour afficher un toast
    function showToast(message, type = 'success') {
        const toast = $('#successToast');
        toast.find('.toast-body').text(message);
        
        if (type === 'error') {
            toast.removeClass('bg-success').addClass('bg-danger');
        } else {
            toast.removeClass('bg-danger').addClass('bg-success');
        }
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    // Ajouter des styles pour les boutons de copie
    $('<style>')
        .text(`
            .text-pre-wrap {
                white-space: pre-wrap;
                font-family: monospace;
                font-size: 0.9em;
                background-color: #f8f9fa;
                padding: 0.5rem;
                border-radius: 4px;
            }
            .history-btn.active {
                background-color: #0d6efd;
                color: white;
            }
            .btn-group .btn {
                margin-left: 0.25rem;
            }
            .copy-to-clipboard.btn-success {
                transition: all 0.3s ease;
            }
        `)
        .appendTo('head');

    // Mettre à jour le compteur lors du chargement initial
    updateVisitesCount();
    loadHistoriqueData();

    // Marquer le bouton d'historique actif
    $(document).on('click', '.history-btn', function() {
        $('.history-btn').removeClass('active');
        $(this).addClass('active');
    });

    // Gérer le clic sur le bouton d'édition dans la liste des visites
    $(document).on('click', '.edit-visite', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        console.log('Edit visite clicked:', id); // Debug log
        
        // Charger les données de la visite
        $.get('api/visites.php?id=' + id, function(visite) {
            console.log('Visite data loaded:', visite); // Debug log
            currentVisite = visite;
            
            // Remplir le formulaire avec les données
            $('#date_visite').val(visite.date_visite);
            $('#heure_visite').val(visite.heure_visite);
            $('#type_visite').val(visite.type_visite);
            $('#status').val('fait');
            $('#honoraire').val(visite.honoraire);
            $('#motif').val(visite.motif);
            $('#histoire_maladie').val(visite.histoire_maladie);
            $('#examens_cliniques').val(visite.examens_cliniques);
            $('#examens_complementaires').val(visite.examens_complementaires);
            $('#diagnostic').val(visite.diagnostic);
            $('#cat').val(visite.cat);
            $('#notes').val(visite.notes);
            $('#temperature').val(visite.temperature);
            $('#poids').val(visite.poids);
            $('#ta').val(visite.ta);
            $('#fc').val(visite.fc);
            $('#sao2').val(visite.sao2);
            
            // Modifier le titre et le bouton du modal
            $('#addVisiteModal .modal-title').html(`Modifier la visite du ${new Date(visite.date_visite).toLocaleDateString('fr-FR')}`);
            $('#addVisiteModal button[type="submit"]').html('<i class="fas fa-save"></i> Enregistrer les modifications');
            
            // Ajouter l'ID de la visite au formulaire
            $('#addVisiteForm').data('edit-id', visite.id);
            
            // Afficher le modal d'édition
            $('#addVisiteModal').modal('show');
        }).fail(function(xhr, status, error) {
            console.error('Error loading visite:', error); // Debug log
            showToast('Erreur lors du chargement de la visite', 'error');
        });
    });

    // Ajouter des styles spécifiques pour la vue détaillée
    $('<style>')
        .text(`
            #viewVisiteModal .card {
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            }

            #viewVisiteModal .card-header {
                background-color: #f8f9fa;
                border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            }

            #viewVisiteModal .card-body label {
                font-size: 0.875rem;
                margin-bottom: 0.25rem;
            }

            #viewVisiteModal .border.rounded {
                min-height: 40px;
                white-space: pre-line;
            }

            /* Compact Vitals Layout */
            .vitals-container {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
            }

            .vitals-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 8px;
            }

            .vital-item {
                display: flex;
                align-items: center;
                gap: 4px;
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                min-height: 32px;
            }

            .vital-label {
                font-size: 11px;
                font-weight: 600;
                color: #495057;
                min-width: 35px;
                text-align: right;
            }

            .vital-input {
                border: none !important;
                box-shadow: none !important;
                padding: 2px 4px !important;
                font-size: 13px !important;
                height: auto !important;
                min-height: 24px !important;
                flex: 1;
                background: transparent;
            }

            .vital-input:focus {
                background-color: #fff3cd !important;
                outline: 1px solid #ffc107 !important;
            }

            .vital-unit {
                font-size: 10px;
                color: #6c757d;
                min-width: 25px;
                text-align: left;
            }

            @media (max-width: 768px) {
                .vitals-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            #viewVisiteModal .badge {
                font-size: 0.875rem;
                padding: 0.5em 0.75em;
            }

            #viewVisiteModal .text-primary {
                color: #0d6efd !important;
            }

            #viewVisiteModal .bg-light {
                background-color: #f8f9fa !important;
            }

            @media (max-width: 768px) {
                #viewVisiteModal .col-md-3 {
                    margin-bottom: 1rem;
                }
            }
        `)
        .appendTo('head');

    // Gérer la suppression d'une visite
    $(document).on('click', '.delete-visite', function() {
        if(confirm('Êtes-vous sûr de vouloir supprimer cette visite ?')) {
            const id = $(this).data('id');
            $.ajax({
                url: 'api/visites.php?id=' + id,
                type: 'DELETE',
                success: function() {
                    loadVisites();
                    showToast('Visite supprimée avec succès');
                },
                error: function() {
                    showToast('Erreur lors de la suppression de la visite', 'error');
                }
            });
        }
    });
});
</script> 