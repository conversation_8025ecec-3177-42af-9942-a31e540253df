<?php
/**
 * Script de migration pour ajouter les constantes vitales à la table visites
 * Date: 2025-06-19
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Migration: Ajout des constantes vitales</h2>";
    
    // Vérifier si les colonnes existent déjà
    $checkQuery = "SHOW COLUMNS FROM visites LIKE 'temperature'";
    $stmt = $db->prepare($checkQuery);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: orange;'>⚠️ Les colonnes des constantes vitales existent déjà dans la table visites.</p>";
    } else {
        // Exécuter la migration
        $migrationSQL = "
            ALTER TABLE `visites` 
            ADD COLUMN `temperature` DECIMAL(4,1) DEFAULT NULL COMMENT 'Température en °C',
            ADD COLUMN `poids` DECIMAL(5,1) DEFAULT NULL COMMENT 'Poids en kg',
            ADD COLUMN `ta` VARCHAR(20) DEFAULT NULL COMMENT 'Tension artérielle en mmHg',
            ADD COLUMN `fc` INT DEFAULT NULL COMMENT 'Fréquence cardiaque en bpm',
            ADD COLUMN `sao2` INT DEFAULT NULL COMMENT 'Saturation en oxygène en %'
        ";
        
        $stmt = $db->prepare($migrationSQL);
        $stmt->execute();
        
        echo "<p style='color: green;'>✅ Migration réussie! Les colonnes suivantes ont été ajoutées à la table visites:</p>";
        echo "<ul>";
        echo "<li><strong>temperature</strong> - DECIMAL(4,1) - Température en °C</li>";
        echo "<li><strong>poids</strong> - DECIMAL(5,1) - Poids en kg</li>";
        echo "<li><strong>ta</strong> - VARCHAR(20) - Tension artérielle en mmHg</li>";
        echo "<li><strong>fc</strong> - INT - Fréquence cardiaque en bpm</li>";
        echo "<li><strong>sao2</strong> - INT - Saturation en oxygène en %</li>";
        echo "</ul>";
    }
    
    // Afficher la structure actuelle de la table
    echo "<h3>Structure actuelle de la table visites:</h3>";
    $describeQuery = "DESCRIBE visites";
    $stmt = $db->prepare($describeQuery);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>Colonne</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Clé</th>";
    echo "<th style='padding: 8px;'>Défaut</th>";
    echo "<th style='padding: 8px;'>Extra</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>{$column['Default']}</td>";
        echo "<td style='padding: 8px;'>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Erreur lors de la migration: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur générale: " . $e->getMessage() . "</p>";
}
?>
