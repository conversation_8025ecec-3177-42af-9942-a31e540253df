# Ajout des Constantes Vitales

## Description
Cette mise à jour ajoute la possibilité d'enregistrer les constantes vitales lors des visites médicales.

## Nouveaux Champs Ajoutés

Dans la section "Examens" du formulaire de visite, les champs suivants ont été ajoutés :

1. **Température (°C)** - Champ numérique avec décimales (ex: 37.2)
2. **Poids (kg)** - Champ numérique avec décimales (ex: 70.5)
3. **TA (mmHg)** - Champ texte pour la tension artérielle (ex: 120/80)
4. **Fc (bpm)** - Champ numérique entier pour la fréquence cardiaque (ex: 80)
5. **SaO2 (%AA)** - Champ numérique entier pour la saturation en oxygène (ex: 98)

## Installation

### 1. Migration de la Base de Données

Avant d'utiliser les nouvelles fonctionnalités, vous devez exécuter la migration pour ajouter les nouvelles colonnes à la table `visites`.

**Option A: Via le navigateur**
1. Ouvrez votre navigateur
2. Allez à l'adresse : `http://votre-domaine/patients/run_migration.php`
3. La migration s'exécutera automatiquement

**Option B: Via phpMyAdmin ou ligne de commande**
1. Exécutez le contenu du fichier `migration_constantes_vitales.sql`

### 2. Vérification

Après la migration, vous devriez voir les nouveaux champs dans :
- Le formulaire d'ajout/modification de visite
- Le modal de visualisation des détails de visite

## Utilisation

### Ajout d'une Nouvelle Visite
1. Cliquez sur "Nouvelle Visite" dans l'onglet Visites
2. Dans la section "Examens", vous verrez maintenant les constantes vitales en haut
3. Remplissez les champs souhaités (tous sont optionnels)
4. Continuez avec le reste du formulaire comme d'habitude

### Modification d'une Visite Existante
1. Cliquez sur le bouton "Modifier" (icône crayon) d'une visite
2. Les constantes vitales apparaîtront dans la section "Examens"
3. Modifiez les valeurs selon vos besoins

### Visualisation des Constantes
1. Cliquez sur le bouton "Voir" (icône œil) d'une visite
2. Dans la colonne "Examens", vous verrez les constantes vitales affichées en haut
3. Les valeurs sont affichées avec leurs unités respectives

## Caractéristiques Techniques

### Types de Données
- `temperature`: DECIMAL(4,1) - Permet des valeurs comme 37.2°C
- `poids`: DECIMAL(5,1) - Permet des valeurs comme 70.5kg
- `ta`: VARCHAR(20) - Texte libre pour formats comme "120/80"
- `fc`: INT - Nombre entier pour les battements par minute
- `sao2`: INT - Nombre entier pour le pourcentage

### Validation
- Tous les champs sont optionnels
- Les champs numériques acceptent les décimales appropriées
- Les valeurs vides sont stockées comme NULL dans la base de données

## Compatibilité

Cette mise à jour est compatible avec :
- Toutes les visites existantes (les nouveaux champs seront vides)
- L'API existante (les nouveaux champs sont optionnels)
- Les fonctionnalités d'export et d'historique

## Support

En cas de problème :
1. Vérifiez que la migration a été exécutée correctement
2. Consultez les logs d'erreur du serveur
3. Assurez-vous que la base de données est accessible
