<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        if(isset($_GET['id'])) {
            // Récupérer un patient spécifique
            $query = "SELECT * FROM patients WHERE id = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$_GET['id']]);
            $patient = $stmt->fetch(PDO::FETCH_ASSOC);
            echo json_encode($patient);
        } elseif(isset($_GET['search'])) {
            // Rechercher des patients
            $searchTerm = '%' . $_GET['search'] . '%';
            $query = "SELECT id, nom, prenom FROM patients 
                     WHERE nom LIKE ? OR prenom LIKE ? 
                     ORDER BY nom, prenom 
                     LIMIT 10";
            $stmt = $db->prepare($query);
            $stmt->execute([$searchTerm, $searchTerm]);
            $patients = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo json_encode($patients);
        } else {
            // Récupérer tous les patients
            $query = "SELECT * FROM patients ORDER BY id DESC";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $patients = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo json_encode($patients);
        }
        break;

    case 'POST':
        // Ajouter un nouveau patient
        $data = json_decode(file_get_contents("php://input"));
        
        // Préparer les valeurs avec des valeurs par défaut pour les champs optionnels
        $nom = $data->nom;
        $prenom = $data->prenom;
        $date_naissance = isset($data->date_naissance) ? $data->date_naissance : null;
        $adresse = isset($data->adresse) ? $data->adresse : null;
        $telephone = isset($data->telephone) ? $data->telephone : null;
        $numero_assurance_sociale = isset($data->numero_assurance_sociale) ? $data->numero_assurance_sociale : null;

        try {
            $query = "INSERT INTO patients (nom, prenom, date_naissance, adresse, telephone, numero_assurance_sociale) 
                      VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($query);
            
            if($stmt->execute([
                $nom,
                $prenom,
                $date_naissance,
                $adresse,
                $telephone,
                $numero_assurance_sociale
            ])) {
                $patientId = $db->lastInsertId();
                http_response_code(201);
                echo json_encode([
                    "message" => "Patient créé avec succès",
                    "id" => $patientId
                ]);
            } else {
                throw new Exception("Erreur lors de l'insertion");
            }
        } catch (Exception $e) {
            http_response_code(503);
            echo json_encode([
                "message" => "Impossible de créer le patient",
                "error" => $e->getMessage()
            ]);
        }
        break;

    case 'PUT':
        // Modifier un patient existant
        $data = json_decode(file_get_contents("php://input"));
        
        // Préparer les valeurs avec des valeurs par défaut pour les champs optionnels
        $nom = $data->nom;
        $prenom = $data->prenom;
        $date_naissance = isset($data->date_naissance) ? $data->date_naissance : null;
        $adresse = isset($data->adresse) ? $data->adresse : null;
        $telephone = isset($data->telephone) ? $data->telephone : null;
        $numero_assurance_sociale = isset($data->numero_assurance_sociale) ? $data->numero_assurance_sociale : null;

        try {
            $query = "UPDATE patients 
                      SET nom = ?, prenom = ?, date_naissance = ?, 
                          adresse = ?, telephone = ?, numero_assurance_sociale = ? 
                      WHERE id = ?";
            $stmt = $db->prepare($query);
            
            if($stmt->execute([
                $nom,
                $prenom,
                $date_naissance,
                $adresse,
                $telephone,
                $numero_assurance_sociale,
                $data->id
            ])) {
                echo json_encode(["message" => "Patient mis à jour avec succès"]);
            } else {
                throw new Exception("Erreur lors de la mise à jour");
            }
        } catch (Exception $e) {
            http_response_code(503);
            echo json_encode([
                "message" => "Impossible de mettre à jour le patient",
                "error" => $e->getMessage()
            ]);
        }
        break;

    case 'DELETE':
        // Supprimer un patient
        $id = isset($_GET['id']) ? $_GET['id'] : die();
        
        $query = "DELETE FROM patients WHERE id = ?";
        $stmt = $db->prepare($query);
        
        if($stmt->execute([$id])) {
            echo json_encode(["message" => "Patient supprimé avec succès"]);
        } else {
            http_response_code(503);
            echo json_encode(["message" => "Impossible de supprimer le patient"]);
        }
        break;
} 